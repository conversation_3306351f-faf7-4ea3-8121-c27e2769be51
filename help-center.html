<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help Center - MyGym Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white"></i>
                    </div>
                    <span class="text-2xl font-bold text-gray-900">MyGym Pro</span>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index1.html" class="text-gray-600 hover:text-blue-600 transition">Home</a>
                    <a href="index1.html#features" class="text-gray-600 hover:text-blue-600 transition">Features</a>
                    <a href="index1.html#pricing" class="text-gray-600 hover:text-blue-600 transition">Pricing</a>
                    <a href="about.html" class="text-gray-600 hover:text-blue-600 transition">About</a>
                    <a href="help-center.html" class="text-blue-600 font-semibold">Help</a>
                </div>
                
                <div class="flex items-center space-x-4">
                    <a href="login.php" class="text-gray-600 hover:text-blue-600 transition">Login</a>
                    <a href="index1.html#pricing" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">Get Started</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 pt-24 pb-20">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <h1 class="text-5xl font-bold text-white mb-6">Help Center</h1>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto mb-8">
                Find answers to common questions and get the help you need to make the most of MyGym Pro.
            </p>
            
            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto">
                <div class="relative">
                    <input type="text" placeholder="Search for help articles..." 
                           class="w-full px-6 py-4 rounded-lg text-gray-900 text-lg focus:outline-none focus:ring-2 focus:ring-white">
                    <button class="absolute right-2 top-2 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Links -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Popular Topics</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 p-6 rounded-2xl hover:bg-blue-100 transition cursor-pointer">
                    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-rocket text-white"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Getting Started</h3>
                    <p class="text-gray-600 text-sm">Learn the basics of setting up your gym</p>
                </div>

                <div class="bg-green-50 p-6 rounded-2xl hover:bg-green-100 transition cursor-pointer">
                    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Member Management</h3>
                    <p class="text-gray-600 text-sm">Add, edit, and manage member profiles</p>
                </div>

                <div class="bg-purple-50 p-6 rounded-2xl hover:bg-purple-100 transition cursor-pointer">
                    <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-credit-card text-white"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Payments & Billing</h3>
                    <p class="text-gray-600 text-sm">Set up payment processing and billing</p>
                </div>

                <div class="bg-orange-50 p-6 rounded-2xl hover:bg-orange-100 transition cursor-pointer">
                    <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Reports & Analytics</h3>
                    <p class="text-gray-600 text-sm">Generate reports and analyze data</p>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Frequently Asked Questions</h2>
            
            <div class="space-y-6">
                <div class="bg-white rounded-lg shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <span class="font-semibold text-gray-900">How do I add new members to my gym?</span>
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </button>
                    <div class="px-6 pb-4 text-gray-600">
                        To add new members, go to the Members section in your dashboard and click "Add Member". 
                        Fill in the required information including name, contact details, and membership plan.
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <span class="font-semibold text-gray-900">How do I set up payment processing?</span>
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </button>
                    <div class="px-6 pb-4 text-gray-600">
                        Navigate to Settings > Payments and connect your preferred payment processor. 
                        We support Stripe, PayPal, and other major payment gateways.
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <span class="font-semibold text-gray-900">Can I import existing member data?</span>
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </button>
                    <div class="px-6 pb-4 text-gray-600">
                        Yes! We provide free data migration services. Contact our support team and we'll help 
                        you import your existing member database from spreadsheets or other gym management systems.
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <span class="font-semibold text-gray-900">Is there a mobile app for members?</span>
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </button>
                    <div class="px-6 pb-4 text-gray-600">
                        Yes, we offer mobile apps for both iOS and Android that allow members to check in, 
                        view their membership status, book classes, and more.
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition">
                        <span class="font-semibold text-gray-900">What kind of support do you provide?</span>
                        <i class="fas fa-chevron-down text-gray-400"></i>
                    </button>
                    <div class="px-6 pb-4 text-gray-600">
                        We offer 24/7 customer support via email, live chat, and phone. We also provide 
                        comprehensive documentation, video tutorials, and free training sessions.
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Support -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-gray-900 mb-6">Still Need Help?</h2>
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Our support team is here to help you succeed. Get in touch with us anytime.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-comments text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Live Chat</h3>
                    <p class="text-gray-600 mb-4">Chat with our support team instantly</p>
                    <button onclick="openLiveChat()" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                        Start Chat
                    </button>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-envelope text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Email Support</h3>
                    <p class="text-gray-600 mb-4">Get detailed help via email</p>
                    <a href="mailto:<EMAIL>" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition inline-block">
                        Send Email
                    </a>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-phone text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Phone Support</h3>
                    <p class="text-gray-600 mb-4">Speak with our experts directly</p>
                    <a href="tel:+15551234567" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition inline-block">
                        Call Now
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <div class="flex items-center justify-center space-x-3 mb-4">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-dumbbell text-white text-sm"></i>
                </div>
                <span class="text-xl font-bold">MyGym Pro</span>
            </div>
            <p class="text-gray-400 mb-4">© 2024 MyGym Pro. All rights reserved.</p>
            <div class="flex justify-center space-x-6">
                <a href="privacy-policy.html" class="text-gray-400 hover:text-white text-sm transition">Privacy Policy</a>
                <a href="terms-of-service.html" class="text-gray-400 hover:text-white text-sm transition">Terms of Service</a>
                <a href="index1.html#contact" class="text-gray-400 hover:text-white text-sm transition">Contact</a>
            </div>
        </div>
    </footer>

    <script>
        // Live Chat Function
        function openLiveChat() {
            alert('Live chat feature would open here. This would typically integrate with a service like Intercom, Zendesk Chat, or Tawk.to');
        }

        // FAQ Toggle
        document.querySelectorAll('.bg-white button').forEach(button => {
            button.addEventListener('click', function() {
                const content = this.nextElementSibling;
                const icon = this.querySelector('i');
                
                if (content.style.display === 'none' || content.style.display === '') {
                    content.style.display = 'block';
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    content.style.display = 'none';
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            });
        });

        // Initially hide all FAQ answers
        document.querySelectorAll('.bg-white .px-6.pb-4').forEach(content => {
            content.style.display = 'none';
        });
    </script>
</body>
</html>
